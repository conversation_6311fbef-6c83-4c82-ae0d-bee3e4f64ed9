import axios, { AxiosInstance } from 'axios';
import { Chat<PERSON><PERSON><PERSON>, <PERSON>l<PERSON><PERSON>ult, DeepseekConfig } from '../types/index.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';
import { ShellTool } from '../tools/ShellTool.js';
import { getSystemPrompt } from '../prompts/SystemPrompt.js';

export class DeepseekProvider {
  private client: AxiosInstance;
  private config: DeepseekConfig;
  private errorHandler: ErrorHandler;
  private shellTool: ShellTool;

  constructor(config: DeepseekConfig) {
    this.config = config;
    this.errorHandler = new ErrorHandler();
    this.shellTool = new ShellTool();
    
    this.client = axios.create({
      baseURL: config.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000
    });
  }

  async chat(messages: ChatMessage[], model?: string): Promise<ChatMessage> {
    const selectedModel = model || this.config.models[0];

    // Separate API call from tool execution to prevent tool re-execution on retries
    const apiResponse = await this.errorHandler.executeWithRetry(async () => {
      // Add system prompt if not present
      const formattedMessages = this.formatMessages(messages);
      if (formattedMessages.length === 0 || formattedMessages[0].role !== 'system') {
        formattedMessages.unshift({
          role: 'system',
          content: getSystemPrompt()
        });
      }



      const response = await this.client.post('/chat/completions', {
        model: selectedModel,
        messages: formattedMessages,
        tools: this.getAvailableTools(),
        tool_choice: 'auto',
        temperature: 0.7,
        max_tokens: 4000,
        stream: false
      });

      const assistantMessage = response.data.choices[0]?.message;
      if (!assistantMessage) {
        throw new Error('No response from Deepseek API');
      }

      return assistantMessage;
    }, 'Deepseek API call');

    // Handle tool calls if present (outside retry logic to prevent re-execution)
    if (apiResponse.tool_calls && apiResponse.tool_calls.length > 0) {
      const toolResults = await this.executeToolCalls(apiResponse.tool_calls);

      return {
        id: this.generateId(),
        role: 'assistant',
        content: apiResponse.content || '',
        timestamp: new Date(),
        toolCalls: apiResponse.tool_calls.map((tc: any) => ({
          id: tc.id,
          name: tc.function.name,
          arguments: JSON.parse(tc.function.arguments)
        })),
        toolResults
      };
    }

    return {
      id: this.generateId(),
      role: 'assistant',
      content: apiResponse.content || '',
      timestamp: new Date()
    };
  }

  private formatMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => {
      const formatted: any = {
        role: msg.role,
        content: msg.content
      };

      // Add tool calls if present
      if (msg.toolCalls && msg.toolCalls.length > 0) {
        formatted.tool_calls = msg.toolCalls.map(tc => ({
          id: tc.id,
          type: 'function',
          function: {
            name: tc.name,
            arguments: JSON.stringify(tc.arguments)
          }
        }));
      }

      // Add tool results if present
      if (msg.toolResults && msg.toolResults.length > 0) {
        // For tool results, we need to create separate tool messages
        return msg.toolResults.map(tr => ({
          role: 'tool',
          tool_call_id: tr.id,
          content: JSON.stringify({
            success: tr.success,
            result: tr.result,
            error: tr.error,
            executionTime: tr.executionTime
          })
        }));
      }

      return formatted;
    }).flat();
  }

  private getAvailableTools(): any[] {
    const shellToolDef = ShellTool.getToolDefinition();
    
    return [
      {
        type: 'function',
        function: {
          name: shellToolDef.name,
          description: shellToolDef.description,
          parameters: shellToolDef.parameters
        }
      }
    ];
  }

  private async executeToolCalls(toolCalls: any[]): Promise<ToolResult[]> {
    const results: ToolResult[] = [];
    
    for (const toolCall of toolCalls) {
      const startTime = Date.now();
      
      try {
        let result: any;
        
        if (toolCall.function.name === 'execute_shell_command') {
          const args = JSON.parse(toolCall.function.arguments);
          result = await this.shellTool.execute({
            command: args.command,
            args: args.args,
            cwd: args.cwd,
            timeout: args.timeout,
            requireApproval: args.requireApproval
          });
        } else {
          throw new Error(`Unknown tool: ${toolCall.function.name}`);
        }
        
        results.push({
          id: toolCall.id,
          name: toolCall.function.name,
          result,
          success: true,
          executionTime: Date.now() - startTime
        });
        
      } catch (error) {
        results.push({
          id: toolCall.id,
          name: toolCall.function.name,
          result: null,
          success: false,
          error: (error as Error).message,
          executionTime: Date.now() - startTime
        });
      }
    }
    
    return results;
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      return response.data.data
        .filter((model: any) => model.id.includes('deepseek'))
        .map((model: any) => model.id);
    } catch (error) {
      this.errorHandler.logWarning('Could not fetch models from Deepseek API, using default models');
      return this.config.models;
    }
  }

  async validateConnection(): Promise<boolean> {
    try {
      await this.client.get('/models');
      return true;
    } catch (error) {
      return false;
    }
  }

  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  updateConfig(config: DeepseekConfig): void {
    this.config = config;
    this.client.defaults.headers['Authorization'] = `Bearer ${config.apiKey}`;
    this.client.defaults.baseURL = config.baseUrl;
  }

  getConfig(): DeepseekConfig {
    return { ...this.config };
  }
}
