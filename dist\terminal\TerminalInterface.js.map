{"version": 3, "file": "TerminalInterface.js", "sourceRoot": "", "sources": ["../../src/terminal/TerminalInterface.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAEhE,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAC1E,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,sBAAsB,EAAE,MAAM,wCAAwC,CAAC;AAChF,OAAO,EAAE,uBAAuB,EAAE,MAAM,yCAAyC,CAAC;AAClF,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AACxE,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,mCAAmC,CAAC;AACvF,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAExD,MAAM,OAAO,iBAAiB;IACpB,aAAa,CAAgB;IAC7B,KAAK,CAAgB;IACrB,gBAAgB,CAAoB;IACpC,cAAc,CAAkB;IAChC,aAAa,CAAyB;IACtC,cAAc,CAA0B;IACxC,SAAS,CAAqB;IAC9B,iBAAiB,CAAoB;IACrC,YAAY,CAAe;IAC3B,EAAE,CAAsB;IAEhC;QACE,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAEjD,IAAI,CAAC,KAAK,GAAG;YACX,UAAU,EAAE,KAAK;YACjB,cAAc,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE;YACvC,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,OAAO,CAAC,GAAG,EAAE;YAC/B,QAAQ,EAAE,UAAU;YACpB,KAAK,EAAE,eAAe;SACvB,CAAC;QAEF,IAAI,CAAC,aAAa,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAChF,IAAI,CAAC,cAAc,GAAG,IAAI,uBAAuB,CAAC;YAChD,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,IAAI;YACrB,gBAAgB,EAAE,GAAG;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,IAAI,kBAAkB,CAAC;YACtC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;YACxB,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,iCAAiC;YAC9C,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC;SAChF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,qBAAqB;YACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YACrD,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAEnC,2BAA2B;YAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC3D,MAAM,UAAU,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC/D,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;gBAEzB,iCAAiC;gBACjC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC5D,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAC5C,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,yBAAyB;YACzB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAEpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAc,EAAE,kBAAkB,CAAC,CAAC;YAC/D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,MAAW;QACvC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC;QAC7C,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC;QAExD,mCAAmC;QACnC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAE3D,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IAChD,CAAC;IAEO,mBAAmB;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,OAAO,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,eAAe,CAAC,OAAO,CAAC,sEAAsE,CAAC,CAAC;QAChG,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;YACjC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;SACzB,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACxB,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC1B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;gBAC9B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAAC;gBACzD,IAAI,CAAC,EAAG,CAAC,MAAM,EAAE,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;gBACzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACjC,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,KAAa;QACrC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,EAAG,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,wBAAwB;YACxB,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC/D,IAAI,OAAO,EAAE,CAAC;oBACZ,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;oBACrC,IAAI,CAAC,EAAG,CAAC,MAAM,EAAE,CAAC;oBAClB,OAAO;gBACT,CAAC;YACH,CAAC;YAED,sBAAsB;YACtB,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAEtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAc,EAAE,gBAAgB,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACrC,IAAI,CAAC,EAAG,CAAC,MAAM,EAAE,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAa;QAC3C,8BAA8B;QAC9B,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC5C,MAAM,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAE1D,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;QAEtD,uBAAuB;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,eAAe,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;YACnF,OAAO;QACT,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAExD,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAEzF,0BAA0B;YAC1B,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;YAE9B,qDAAqD;YACrD,MAAM,iBAAiB,GAAG,eAAe,CAAC,uBAAuB,CAAC,QAAQ,EAAE;gBAC1E,WAAW,EAAE,SAAS;gBACtB,iBAAiB,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC;gBACzE,UAAU,EAAE,IAAI;gBAChB,eAAe,EAAE,KAAK;aACvB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEzC,0BAA0B;YAC1B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAEzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;YAE9B,IAAK,KAAe,CAAC,OAAO,KAAK,mBAAmB,EAAE,CAAC;gBACrD,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAc,EAAE,iBAAiB,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAClC,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YACxC;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,yDAAyD;gBAClE,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CAAC;QAEH,IAAI,OAAO,EAAE,CAAC;YACZ,eAAe,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACvD,2BAA2B;YAC3B,0EAA0E;QAC5E,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC,cAAc,CAAC;QAC7B,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,SAAS;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnE,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;QAEvD,OAAO,GAAG,MAAM,IAAI,QAAQ,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC5D,CAAC;IAEO,uBAAuB;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC;IAC3C,CAAC;IAEO,UAAU;QAChB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IAC5E,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;CACF"}