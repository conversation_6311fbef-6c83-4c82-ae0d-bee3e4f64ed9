import axios from 'axios';

async function testDeepseekAPI() {
  const client = axios.create({
    baseURL: 'https://api.deepseek.com',
    headers: {
      'Authorization': 'Bearer sk-931c6a1daf1a4a299fea41bd5ac78e49',
      'Content-Type': 'application/json'
    },
    timeout: 60000
  });

  const tools = [
    {
      type: 'function',
      function: {
        name: 'execute_shell_command',
        description: 'Execute shell commands and capture their output.',
        parameters: {
          type: 'object',
          properties: {
            command: {
              type: 'string',
              description: 'The shell command to execute'
            },
            args: {
              type: 'array',
              description: 'Optional array of command arguments'
            },
            cwd: {
              type: 'string',
              description: 'Working directory for command execution'
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds'
            },
            requireApproval: {
              type: 'boolean',
              description: 'Whether to require user approval before execution'
            }
          },
          required: ['command']
        }
      }
    }
  ];

  const messages = [
    {
      role: 'system',
      content: 'You are a helpful CLI assistant that can execute shell commands.'
    },
    {
      role: 'user',
      content: 'list files in current directory'
    }
  ];

  try {
    console.log('Testing Deepseek API...');
    console.log('Request payload:', JSON.stringify({
      model: 'deepseek-chat',
      messages,
      tools,
      tool_choice: 'auto',
      temperature: 0.7,
      max_tokens: 4000,
      stream: false
    }, null, 2));

    const response = await client.post('/chat/completions', {
      model: 'deepseek-chat',
      messages,
      tools,
      tool_choice: 'auto',
      temperature: 0.7,
      max_tokens: 4000,
      stream: false
    });

    console.log('API Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('API Error:', error.response?.status, error.response?.statusText);
    console.error('Error details:', error.response?.data);
    console.error('Full error:', error.message);
  }
}

testDeepseekAPI();
