{"version": 3, "file": "DeepseekProvider.js", "sourceRoot": "", "sources": ["../../src/providers/DeepseekProvider.ts"], "names": [], "mappings": "AAAA,OAAO,KAAwB,MAAM,OAAO,CAAC;AAE7C,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAE7D,MAAM,OAAO,gBAAgB;IACnB,MAAM,CAAgB;IACtB,MAAM,CAAiB;IACvB,YAAY,CAAe;IAC3B,SAAS,CAAY;IAE7B,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;QAEjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,MAAM,CAAC,MAAM,EAAE;gBAC1C,cAAc,EAAE,kBAAkB;aACnC;YACD,OAAO,EAAE,KAAK;SACf,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAuB,EAAE,KAAc;QAChD,MAAM,aAAa,GAAG,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAErD,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;YACnD,mCAAmC;YACnC,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC7E,iBAAiB,CAAC,OAAO,CAAC;oBACxB,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,eAAe,EAAE;iBAC3B,CAAC,CAAC;YACL,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3D,KAAK,EAAE,aAAa;gBACpB,QAAQ,EAAE,iBAAiB;gBAC3B,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE;gBAC/B,WAAW,EAAE,MAAM;gBACnB,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;YAC3D,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;YAED,+BAA+B;YAC/B,IAAI,gBAAgB,CAAC,UAAU,IAAI,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;gBAE7E,OAAO;oBACL,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;oBACrB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,gBAAgB,CAAC,OAAO,IAAI,EAAE;oBACvC,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,SAAS,EAAE,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,CAAC;wBACvD,EAAE,EAAE,EAAE,CAAC,EAAE;wBACT,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;wBACtB,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;qBAC7C,CAAC,CAAC;oBACH,WAAW;iBACZ,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;gBACrB,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,gBAAgB,CAAC,OAAO,IAAI,EAAE;gBACvC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC,EAAE,mBAAmB,CAAC,CAAC;IAC1B,CAAC;IAEO,cAAc,CAAC,QAAuB;QAC5C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,SAAS,GAAQ;gBACrB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC;YAEF,4BAA4B;YAC5B,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAC9C,EAAE,EAAE,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,EAAE,CAAC,IAAI;wBACb,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;qBACxC;iBACF,CAAC,CAAC,CAAC;YACN,CAAC;YAED,8BAA8B;YAC9B,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,6DAA6D;gBAC7D,OAAO,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAChC,IAAI,EAAE,MAAM;oBACZ,YAAY,EAAE,EAAE,CAAC,EAAE;oBACnB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;wBACtB,OAAO,EAAE,EAAE,CAAC,OAAO;wBACnB,MAAM,EAAE,EAAE,CAAC,MAAM;wBACjB,KAAK,EAAE,EAAE,CAAC,KAAK;wBACf,aAAa,EAAE,EAAE,CAAC,aAAa;qBAChC,CAAC;iBACH,CAAC,CAAC,CAAC;YACN,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACZ,CAAC;IAEO,iBAAiB;QACvB,MAAM,YAAY,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;QAEnD,OAAO;YACL;gBACE,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,YAAY,CAAC,IAAI;oBACvB,WAAW,EAAE,YAAY,CAAC,WAAW;oBACrC,UAAU,EAAE,YAAY,CAAC,UAAU;iBACpC;aACF;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,SAAgB;QAC7C,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE7B,IAAI,CAAC;gBACH,IAAI,MAAW,CAAC;gBAEhB,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,uBAAuB,EAAE,CAAC;oBACvD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBACrD,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;wBACpC,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,GAAG,EAAE,IAAI,CAAC,GAAG;wBACb,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,eAAe,EAAE,IAAI,CAAC,eAAe;qBACtC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,iBAAiB,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC7D,CAAC;gBAED,OAAO,CAAC,IAAI,CAAC;oBACX,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;oBAC5B,MAAM;oBACN,OAAO,EAAE,IAAI;oBACb,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC;oBACX,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;oBAC5B,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,KAAK;oBACd,KAAK,EAAG,KAAe,CAAC,OAAO;oBAC/B,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI;iBACtB,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;iBACrD,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,gEAAgE,CAAC,CAAC;YAC/F,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEO,UAAU;QAChB,OAAO,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IAED,YAAY,CAAC,MAAsB;QACjC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,MAAM,CAAC,MAAM,EAAE,CAAC;QAC1E,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAChD,CAAC;IAED,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF"}