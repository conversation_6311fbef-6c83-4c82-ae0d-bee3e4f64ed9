import axios from 'axios';
import { <PERSON>rror<PERSON>andler } from '../utils/ErrorHandler.js';
import { ShellTool } from '../tools/ShellTool.js';
import { getSystemPrompt } from '../prompts/SystemPrompt.js';
export class DeepseekProvider {
    client;
    config;
    errorHandler;
    shellTool;
    constructor(config) {
        this.config = config;
        this.errorHandler = new ErrorHandler();
        this.shellTool = new ShellTool();
        this.client = axios.create({
            baseURL: config.baseUrl,
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            timeout: 60000
        });
    }
    async chat(messages, model) {
        const selectedModel = model || this.config.models[0];
        // Separate API call from tool execution to prevent tool re-execution on retries
        const apiResponse = await this.errorHandler.executeWithRetry(async () => {
            // Add system prompt if not present
            const formattedMessages = this.formatMessages(messages);
            if (formattedMessages.length === 0 || formattedMessages[0].role !== 'system') {
                formattedMessages.unshift({
                    role: 'system',
                    content: getSystemPrompt()
                });
            }
            const response = await this.client.post('/chat/completions', {
                model: selectedModel,
                messages: formattedMessages,
                tools: this.getAvailableTools(),
                tool_choice: 'auto',
                temperature: 0.7,
                max_tokens: 4000,
                stream: false
            });
            const assistantMessage = response.data.choices[0]?.message;
            if (!assistantMessage) {
                throw new Error('No response from Deepseek API');
            }
            return assistantMessage;
        }, 'Deepseek API call');
        // Handle tool calls if present (outside retry logic to prevent re-execution)
        if (apiResponse.tool_calls && apiResponse.tool_calls.length > 0) {
            const toolResults = await this.executeToolCalls(apiResponse.tool_calls);
            return {
                id: this.generateId(),
                role: 'assistant',
                content: apiResponse.content || '',
                timestamp: new Date(),
                toolCalls: apiResponse.tool_calls.map((tc) => ({
                    id: tc.id,
                    name: tc.function.name,
                    arguments: JSON.parse(tc.function.arguments)
                })),
                toolResults
            };
        }
        return {
            id: this.generateId(),
            role: 'assistant',
            content: apiResponse.content || '',
            timestamp: new Date()
        };
    }
    formatMessages(messages) {
        return messages.map(msg => {
            const formatted = {
                role: msg.role,
                content: msg.content
            };
            // Add tool calls if present
            if (msg.toolCalls && msg.toolCalls.length > 0) {
                formatted.tool_calls = msg.toolCalls.map(tc => ({
                    id: tc.id,
                    type: 'function',
                    function: {
                        name: tc.name,
                        arguments: JSON.stringify(tc.arguments)
                    }
                }));
            }
            // Add tool results if present
            if (msg.toolResults && msg.toolResults.length > 0) {
                // For tool results, we need to create separate tool messages
                return msg.toolResults.map(tr => ({
                    role: 'tool',
                    tool_call_id: tr.id,
                    content: JSON.stringify({
                        success: tr.success,
                        result: tr.result,
                        error: tr.error,
                        executionTime: tr.executionTime
                    })
                }));
            }
            return formatted;
        }).flat();
    }
    getAvailableTools() {
        const shellToolDef = ShellTool.getToolDefinition();
        return [
            {
                type: 'function',
                function: {
                    name: shellToolDef.name,
                    description: shellToolDef.description,
                    parameters: shellToolDef.parameters
                }
            }
        ];
    }
    async executeToolCalls(toolCalls) {
        const results = [];
        for (const toolCall of toolCalls) {
            const startTime = Date.now();
            try {
                let result;
                if (toolCall.function.name === 'execute_shell_command') {
                    const args = JSON.parse(toolCall.function.arguments);
                    result = await this.shellTool.execute({
                        command: args.command,
                        args: args.args,
                        cwd: args.cwd,
                        timeout: args.timeout,
                        requireApproval: args.requireApproval
                    });
                }
                else {
                    throw new Error(`Unknown tool: ${toolCall.function.name}`);
                }
                results.push({
                    id: toolCall.id,
                    name: toolCall.function.name,
                    result,
                    success: true,
                    executionTime: Date.now() - startTime
                });
            }
            catch (error) {
                results.push({
                    id: toolCall.id,
                    name: toolCall.function.name,
                    result: null,
                    success: false,
                    error: error.message,
                    executionTime: Date.now() - startTime
                });
            }
        }
        return results;
    }
    async getAvailableModels() {
        try {
            const response = await this.client.get('/models');
            return response.data.data
                .filter((model) => model.id.includes('deepseek'))
                .map((model) => model.id);
        }
        catch (error) {
            this.errorHandler.logWarning('Could not fetch models from Deepseek API, using default models');
            return this.config.models;
        }
    }
    async validateConnection() {
        try {
            await this.client.get('/models');
            return true;
        }
        catch (error) {
            return false;
        }
    }
    generateId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    updateConfig(config) {
        this.config = config;
        this.client.defaults.headers['Authorization'] = `Bearer ${config.apiKey}`;
        this.client.defaults.baseURL = config.baseUrl;
    }
    getConfig() {
        return { ...this.config };
    }
}
//# sourceMappingURL=DeepseekProvider.js.map